import { createRouter, createWebHistory } from 'vue-router';
import CustomerLayout from '@/layouts/CustomerLayout.vue';
import DesignerLayout from '@/layouts/DesignerLayout.vue';
import LiveKitLayout from '@/layouts/LiveKitLayout.vue';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      meta: {
        layout: CustomerLayout,
        headerTitle: '首頁',
        needLogin: false
      },
      name: 'customerhome',
      component: () => import('../views/IndexView.vue')
    },
    {
      path: '/meeting/:roomId/:meetToken',
      meta: {
        layout: LiveKitLayout,
        headerTitle: '視訊會議',
        needLogin: true
      },
      name: 'meeting',
      component: () => import('../views/LiveKitView.vue')
    },
    {
      path: '/p/:redirectPath',
      meta: {
        layout: CustomerLayout,
        headerTitle: '設計師作品頁跳轉',
        needLogin: false
      },
      name: 'portfolioredirect',
      component: () => import('../views/RedirectView.vue')
    },
    {
      path: '/designer/register',
      meta: {
        layout: DesignerLayout,
        headerTitle: '註冊/登入',
        needLogin: false
      },
      name: 'designerregister',
      component: () => import('../views/Designer/DesignerRegisterLoginView.vue')
    },
    {
      path: '/designer/home',
      meta: {
        layout: DesignerLayout,
        headerTitle: '首頁',
        needLogin: false
      },
      name: 'designerhome',
      component: () => import('../views/Designer/DesignerIndexView.vue')
    },
    {
      path: '/designer/portfolio/:designerId',
      meta: {
        layout: DesignerLayout,
        headerTitle: '作品主頁',
        needLogin: true
      },
      name: 'designerportfolio',
      component: () => import('../views/Designer/DesignerPortfolioView.vue')
    },
    {
      path: '/customer/portfolio/:designerId',
      meta: {
        layout: CustomerLayout,
        headerTitle: '設計師作品',
        needLogin: false
      },
      name: 'customerportfolio',
      component: () => import('../views/Customer/CustomerPortfolioView.vue')
    },
    {
      path: '/designer/orderlist',
      meta: {
        layout: DesignerLayout,
        headerTitle: '訂單列表',
        needLogin: true
      },
      name: 'designerOrderList',
      component: () => import('../views/Designer/DesignerOrderListView.vue')
    },
    {
      path: '/designer/orderdetail/measure/unaccepted/:id',
      meta: {
        layout: DesignerLayout,
        headerTitle: '空間丈量',
        needLogin: true
      },
      name: 'MeasureUnacceptedOrderDetail',
      component: () => import('../views/Designer/OrderDetail/MeasureUnacceptedView.vue')
    },
    {
      path: '/designer/orderdetail/design/unaccepted/:id',
      meta: {
        layout: DesignerLayout,
        headerTitle: '室內設計',
        needLogin: true
      },
      name: 'DesignUnacceptedOrderDetail',
      component: () => import('../views/Designer/OrderDetail/DesignUnacceptedView.vue')
    },
    {
      path: '/designer/orderdetail/construction/unaccepted/:id',
      meta: {
        layout: DesignerLayout,
        headerTitle: '裝潢施工',
        needLogin: true
      },
      name: 'ConstructionUnacceptedOrderDetail',
      component: () => import('../views/Designer/OrderDetail/ConstructionUnacceptedView.vue')
    },
    {
      path: '/designer/orderdetail/measure/accepted/:id',
      meta: {
        layout: DesignerLayout,
        headerTitle: '空間丈量',
        needLogin: true
      },
      name: 'MeasureAcceptedOrderDetail',
      component: () => import('../views/Designer/OrderDetail/MeasureAcceptedView.vue')
    },
    {
      path: '/designer/orderdetail/design/accepted/:id',
      meta: {
        layout: DesignerLayout,
        headerTitle: '室內設計',
        needLogin: true
      },
      name: 'DesignAcceptedOrderDetail',
      component: () => import('../views/Designer/OrderDetail/DesignAcceptedView.vue')
    },
    {
      path: '/designer/orderdetail/construction/accepted/:id',
      meta: {
        layout: DesignerLayout,
        headerTitle: '裝潢施工',
        needLogin: true
      },
      name: 'ConstructionAcceptedOrderDetail',
      component: () => import('../views/Designer/OrderDetail/ConstructionAcceptedView.vue')
    },
    {
      path: '/customer/terms',
      meta: {
        layout: CustomerLayout,
        headerTitle: '服務條款',
        needLogin: false
      },
      name: 'customerterms',
      component: () => import('../views/TermsView.vue')
    },
    {
      path: '/designer/terms',
      meta: {
        layout: DesignerLayout,
        headerTitle: '服務條款',
        needLogin: false
      },
      name: 'designerterms',
      component: () => import('../views/TermsView.vue')
    },
    {
      path: '/customer/appdownload',
      meta: {
        layout: CustomerLayout,
        headerTitle: '使用者App',
        needLogin: false
      },
      name: 'customerappdownload',
      component: () => import('../views/Customer/CustomerAppDownloadView.vue')
    },
    {
      path: '/designer/appdownload',
      meta: {
        layout: DesignerLayout,
        headerTitle: '設計師App',
        needLogin: false
      },
      name: 'designerappdownload',
      component: () => import('../views/Designer/DesignerAppDownloadView.vue')
    },
    {
      path: '/designer/appdownload/redirect',
      meta: {
        layout: DesignerLayout,
        headerTitle: '跳轉設計師App',
        needLogin: false
      },
      name: 'designerredirectappdownload',
      component: () => import('../views/Designer/DesignerAppDownloadRedirectView.vue')
    },
    {
      path: '/designer/profile',
      meta: {
        layout: DesignerLayout,
        headerTitle: '個人資訊',
        needLogin: true
      },
      name: 'designerprofile',
      component: () => import('../views/Designer/DesignerProfileView.vue')
    },
    {
      path: '/customer/measure/publish',
      meta: {
        layout: CustomerLayout,
        headerTitle: '預約到府丈量服務',
        needLogin: false
      },
      name: 'customermeasurepublish',
      component: () => import('../views/Customer/CustomerMeasurePublishView.vue')
    },
    {
      path: '/customer/designcompany',
      meta: {
        layout: CustomerLayout,
        headerTitle: '設計公司',
        needLogin: false
      },
      name: 'designcompany',
      component: () => import('../views/Customer/CustomerDesignCompanyListView.vue')
    },
    {
      path: '/customer/askservice',
      meta: {
        layout: CustomerLayout,
        headerTitle: '服務項目',
        needLogin: false
      },
      name: 'askservice',
      component: () => import('../views/Customer/CustomerAskService.vue')
    },
    {
      path: '/customer/bigdata',
      meta: {
        layout: CustomerLayout,
        headerTitle: '大數據估價',
        needLogin: false
      },
      name: 'bigdata',
      component: () => import('../views/Customer/BigDataView.vue')
    },
    {
      path: '/customer/measure/publishok',
      meta: {
        layout: CustomerLayout,
        headerTitle: '訂單刊登成功',
        needLogin: false
      },
      name: 'customermeasurepublishok',
      component: () => import('../views/Customer/CustomerMeasurePublishOkView.vue')
    },
    {
      path: '/customer/measure/duplicate-address',
      meta: {
        layout: CustomerLayout,
        headerTitle: '重複地址確認',
        needLogin: true
      },
      name: 'customerduplicateaddress',
      component: () => import('../views/Customer/CustomerDuplicateAddressView.vue')
    },
    {
      path: '/customer/register',
      meta: {
        layout: CustomerLayout,
        headerTitle: '註冊/登入',
        needLogin: false
      },
      name: 'customerregister',
      component: () => import('../views/Customer/CustomerUnifiedLogin.vue'),
      props: { mode: 'full', redirectRoute: 'customerhome' }
    },
    {
      path: '/customer/simpleregister',
      meta: {
        layout: CustomerLayout,
        headerTitle: '註冊/登入',
        needLogin: false
      },
      name: 'customersimpleregister',
      component: () => import('../views/Customer/CustomerUnifiedLogin.vue'),
      props: { mode: 'simple', redirectRoute: 'customerorderlist' }
    },
    {
      path: '/customer/orderList',
      meta: {
        layout: CustomerLayout,
        headerTitle: '訂單紀錄',
        needLogin: true
      },
      name: 'customerorderlist',
      component: () => import('../views/Customer/CustomerOrderListView.vue')
    },
    {
      path: '/customer/orderdetail/measure/:id',
      meta: {
        layout: CustomerLayout,
        headerTitle: '空間丈量',
        needLogin: true
      },
      name: 'customermeasuredetail',
      component: () => import('../views/Customer/CustomerMeasureDetailView.vue')
    },
    {
      path: '/customer/orderdetail/design/:id',
      meta: {
        layout: CustomerLayout,
        headerTitle: '室內設計',
        needLogin: true
      },
      name: 'customerdesigndetail',
      component: () => import('../views/Customer/CustomerDesignDetailView.vue')
    },
    {
      path: '/customer/orderdetail/construction/:id',
      meta: {
        layout: CustomerLayout,
        headerTitle: '裝潢施工',
        needLogin: true
      },
      name: 'customerconstructiondetail',
      component: () => import('../views/Customer/CustomerConstructionDetailView.vue')
    },
    {
      path: '/customer/orderdetail/invoice/:id',
      meta: {
        layout: CustomerLayout,
        headerTitle: '發票節稅',
        needLogin: true
      },
      name: 'customerinvoice',
      component: () => import('../views/Customer/CustomerInvoiceView.vue')
    },
    {
      path: '/:redirectPath',
      meta: {
        layout: CustomerLayout,
        headerTitle: '設計師作品頁跳轉',
        needLogin: false
      },
      name: 'portfolioredirect',
      component: () => import('../views/RedirectView.vue')
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      redirect: { name: 'customerhome' },
      component: () => import('../views/IndexView.vue'),
      children: []
    }
  ],
  scrollBehavior(_to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

/**
 * 根據目標路由決定使用哪個客戶登入頁面
 * @param routeName 目標路由名稱
 * @returns 登入頁面路由名稱
 */
function getCustomerLoginRoute(routeName: string): string {
  // 需要使用簡化版登入頁面的路由
  const simpleLoginRoutes = ['customerorderlist'];

  if (simpleLoginRoutes.includes(routeName)) {
    return 'customersimpleregister';
  }

  // 默認使用完整版登入頁面
  return 'customerregister';
}

router.beforeEach(async (to, _from, next) => {
  const { loginState: customerLoggedIn } = useCustomerInfoStore();
  const { loginState: designerLoggedIn } = useDesignerInfoStore();

  const needLogin = to.matched.some(record => record.meta.needLogin);
  const isCustomerRoute = to.path.startsWith('/customer');
  const isDesignerRoute = to.path.startsWith('/designer');

  const userAgent = navigator.userAgent.toLowerCase();// 檢查是否為 Line 內建瀏覽器
  // console.log('瀏覽器：', userAgent);
  const isLineBrowser = userAgent.includes('line');
  // 檢查 URL 中是否包含 ?openExternalBrowser=1
  const urlParams = new URLSearchParams(window.location.search);
  const hasOpenExternalBrowser = urlParams.has('openExternalBrowser');
  if (isLineBrowser) {
    if (!hasOpenExternalBrowser) {
      // 如果是 Line 瀏覽器且 URL 中沒有 openExternalBrowser 參數，則添加它
      urlParams.append('openExternalBrowser', '1');
      const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
      window.location.replace(newUrl);
    }
  } else {
    if (hasOpenExternalBrowser) {
      // 如果不是 Line 瀏覽器且 URL 中有 openExternalBrowser 參數，則移除它
      urlParams.delete('openExternalBrowser');
      const newUrl = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
      window.location.replace(newUrl);
    }
  }

  if (!needLogin) {
    return next();
  }

  // 統一的客戶端登入重定向邏輯
  if (isCustomerRoute || to.path === '/') {
    if (!customerLoggedIn) {
      // 根據目標頁面決定使用哪個登入頁面
      const redirectRoute = getCustomerLoginRoute(to.name as string);
      return next({ name: redirectRoute });
    }
    return next();
  }

  if (isDesignerRoute) {
    return designerLoggedIn ? next() : next({ name: 'designerhome' });
  }

  next();
});

//設定各種meta、標題、icon相關的
router.afterEach((to) => {
  // --- 設定 favicon ---
  let favicon = document.querySelector('link[rel="icon"]');
  if (!favicon) {
    favicon = document.createElement('link');
    favicon.setAttribute('rel', 'icon');
    document.head.appendChild(favicon);
  }

  if (to.path.startsWith('/meeting')) {
    favicon.setAttribute('href', '/livekit.ico');
  } else if (to.path.startsWith('/designer')) {
    favicon.setAttribute('href', '/Designer_Hez_Icon.ico');
  } else {
    favicon.setAttribute('href', '/Customer_Hez_Icon.ico');
  }

  // --- 設定 title ---
  const title = to.meta.headerTitle as string || '家易(HomeEasy)';
  if (to.path.startsWith('/designer')) {
    document.title = `${title} - 家易(HomeEasy) - 設計師端`;
  } else {
    document.title = `${title} - 家易(HomeEasy) - 客戶端`;
  }
});

/**
 * 這邊是為了解決網頁上版之後有些人沒有刷新網頁造成動態載入模組失敗時，重新整理頁面
 */
router.onError((error, to) => {
  if (
    error.message.includes('Failed to fetch dynamically imported module') ||
    error.message.includes('Importing a module script failed')
  ) {
    console.log('有吃到Error', to);
    if (!to?.fullPath) {
      window.location.reload();
    } else {
      window.location.href = to.fullPath;
    }
  }
});

export default router;
